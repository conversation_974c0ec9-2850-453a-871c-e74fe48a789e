#!/usr/bin/env python3
"""
Test script to verify that the AI summary fix works correctly.
This script tests that insert_and_update_df_to_GZ_id only returns truly new cases.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_id, insert_and_update_df_to_GZ_id_with_all_records

def test_new_vs_existing_cases():
    """
    Test that insert_and_update_df_to_GZ_id only returns new cases,
    not existing cases that were updated.
    """
    print("🧪 Testing AI Summary Fix...")
    
    # Create test data - mix of new and potentially existing cases
    test_cases = pd.DataFrame([
        {
            'docket': 'TEST-NEW-001',
            'court': 'TEST-COURT',
            'title': 'Test New Case 1',
            'date_filed': '2025-07-24',
            'aisummary': None  # New case should not have AI summary
        },
        {
            'docket': 'TEST-NEW-002', 
            'court': 'TEST-COURT',
            'title': 'Test New Case 2',
            'date_filed': '2025-07-24',
            'aisummary': None  # New case should not have AI summary
        }
    ])
    
    print(f"📝 Testing with {len(test_cases)} test cases...")
    
    # Test the modified function
    try:
        result_new_only = insert_and_update_df_to_GZ_id(test_cases, "tb_case", "docket", "court")
        print(f"✅ insert_and_update_df_to_GZ_id returned {len(result_new_only)} cases")
        
        # Test the all-records function for comparison
        result_all = insert_and_update_df_to_GZ_id_with_all_records(test_cases, "tb_case", "docket", "court")
        print(f"✅ insert_and_update_df_to_GZ_id_with_all_records returned {len(result_all)} cases")
        
        # Check if any returned cases have aisummary populated
        if not result_new_only.empty:
            has_aisummary = result_new_only['aisummary'].notna().any()
            print(f"🔍 New cases have AI summary populated: {has_aisummary}")
            
            if has_aisummary:
                print("❌ ISSUE: New cases should not have AI summary populated!")
                return False
            else:
                print("✅ GOOD: New cases do not have AI summary populated")
                return True
        else:
            print("ℹ️ No new cases returned (all were existing cases)")
            return True
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    try:
        from DatabaseManagement.ImportExport import get_gz_connection
        conn = get_gz_connection()
        cursor = conn.cursor()
        
        # Delete test cases
        cursor.execute("DELETE FROM tb_case WHERE docket LIKE 'TEST-NEW-%' AND court = 'TEST-COURT'")
        conn.commit()
        cursor.close()
        conn.close()
        print("🧹 Cleaned up test data")
    except Exception as e:
        print(f"⚠️ Could not clean up test data: {e}")

if __name__ == '__main__':
    try:
        success = test_new_vs_existing_cases()
        if success:
            print("\n🎉 AI Summary fix test PASSED!")
        else:
            print("\n❌ AI Summary fix test FAILED!")
    finally:
        cleanup_test_data()
